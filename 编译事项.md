# 准备工作

下载llvm+clang:  https://github.com/llvm/llvm-project/releases/download/llvmorg-20.1.5/clang+llvm-20.1.5-x86_64-pc-windows-msvc.tar.xz

配置系统变量:
LIBCLANG_PATH= {解压后的lib目录所在地址}

```shell
Set-ExecutionPolicy Bypass -Scope Process -Force; `
[System.Net.ServicePointManager]::SecurityProtocol = [System.Net.ServicePointManager]::SecurityProtocol -bor 3072; `
iex ((New-Object System.Net.WebClient).DownloadString('https://community.chocolatey.org/install.ps1'))
```


```shell
choco install 7zip
```

## 交叉编译

### 方案1：安装clang依赖（推荐）
```shell
docker run --rm -v .:/opt -w /opt rust:latest bash -c "apt-get update && apt-get install -y clang libclang-dev && rustup target add x86_64-unknown-linux-gnu && cargo build --release --target x86_64-unknown-linux-gnu"
```

### 方案2：使用预装clang的镜像
```shell
docker run --rm -v .:/opt -w /opt rust:latest bash -c "apt-get update && apt-get install -y build-essential clang libclang-dev pkg-config && rustup target add x86_64-unknown-linux-gnu && cargo build --release --target x86_64-unknown-linux-gnu"
```

### 方案3：使用自定义Dockerfile（最稳定）
```shell
# 构建自定义镜像
docker build -f Dockerfile.cross-compile -t easytier-builder .

# 使用自定义镜像编译
docker run --rm -v .:/opt easytier-builder

# 或者指定具体命令
docker run --rm -v .:/opt easytier-builder cargo build --release --target x86_64-unknown-linux-gnu
```

### 方案4：一次性解决方案（最简单）
```shell
docker run --rm -v .:/opt -w /opt \
  -e LIBCLANG_PATH=/usr/lib/llvm-14/lib \
  rust:latest bash -c "
    apt-get update &&
    apt-get install -y clang libclang-dev &&
    rustup target add x86_64-unknown-linux-gnu &&
    cargo build --release --target x86_64-unknown-linux-gnu
  "
```

### 原始命令（可能失败）
```shell
docker run --rm -v /c/Users/<USER>/Desktop/temp/rust/EasyTier:/opt -w /opt rust:latest bash -c "rustup target add x86_64-unknown-linux-gnu && cargo build --release --target x86_64-unknown-linux-gnu"

docker run --rm -v .:/opt -w /opt rust:latest bash -c "rustup target add x86_64-unknown-linux-gnu && cargo build --release --target x86_64-unknown-linux-gnu"
```

## 故障排除

### libclang错误解决方案
如果遇到 `Unable to find libclang` 错误，按以下步骤解决：

1. **确保安装了clang**：
   ```shell
   apt-get update && apt-get install -y clang libclang-dev
   ```

2. **设置LIBCLANG_PATH环境变量**：
   ```shell
   export LIBCLANG_PATH=/usr/lib/llvm-14/lib
   # 或者
   export LIBCLANG_PATH=/usr/lib/x86_64-linux-gnu
   ```

3. **查找libclang位置**：
   ```shell
   find /usr -name "libclang.so*" 2>/dev/null
   ```

### 常见问题
- **权限问题**：确保Docker有权限访问挂载的目录
- **网络问题**：如果apt-get失败，可能需要配置代理或使用国内镜像
- **磁盘空间**：确保有足够的磁盘空间进行编译

### 推荐使用方案
建议使用**方案4（一次性解决方案）**，因为它最简单且包含了所有必要的依赖。