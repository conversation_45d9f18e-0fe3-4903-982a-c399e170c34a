# 准备工作

下载llvm+clang:  https://github.com/llvm/llvm-project/releases/download/llvmorg-20.1.5/clang+llvm-20.1.5-x86_64-pc-windows-msvc.tar.xz

配置系统变量:
LIBCLANG_PATH= {解压后的lib目录所在地址}

```shell
Set-ExecutionPolicy Bypass -Scope Process -Force; `
[System.Net.ServicePointManager]::SecurityProtocol = [System.Net.ServicePointManager]::SecurityProtocol -bor 3072; `
iex ((New-Object System.Net.WebClient).DownloadString('https://community.chocolatey.org/install.ps1'))
```


```shell
choco install 7zip
```

## 交叉编译

```shell
docker run --rm -v /c/Users/<USER>/Desktop/temp/rust/EasyTier:/opt -w /opt rust:latest bash -c "rustup target add x86_64-unknown-linux-gnu && cargo build --release --target x86_64-unknown-linux-gnu"
```