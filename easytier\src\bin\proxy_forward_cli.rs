use std::collections::HashMap;

use anyhow::Result;
use clap::{Parser, Subcommand};
use serde_json;

use easytier::common::config::{
    ConfigLoader, ProxyForwardConfig, ProxyForwardEnvironment, TomlConfigLoader,
};

#[derive(Parser)]
#[command(name = "proxy-forward-cli")]
#[command(about = "EasyTier代理转发配置管理工具")]
struct Cli {
    #[command(subcommand)]
    command: Commands,
}

#[derive(Subcommand)]
enum Commands {
    /// 添加代理转发环境
    Add {
        /// 配置文件路径
        #[arg(short, long)]
        config: String,
        /// 环境名称
        #[arg(short, long)]
        env: String,
        /// 服务器地址
        #[arg(short, long)]
        server: String,
        /// 端口映射 (格式: 本地端口-远程端口)
        #[arg(short, long)]
        ports: Vec<String>,
    },
    /// 删除代理转发环境
    Remove {
        /// 配置文件路径
        #[arg(short, long)]
        config: String,
        /// 环境名称
        #[arg(short, long)]
        env: String,
    },
    /// 列出所有代理转发配置
    List {
        /// 配置文件路径
        #[arg(short, long)]
        config: String,
        /// 输出格式 (table, json)
        #[arg(short, long, default_value = "table")]
        format: String,
    },
    /// 验证代理转发配置
    Validate {
        /// 配置文件路径
        #[arg(short, long)]
        config: String,
    },
    /// 生成示例配置
    Example {
        /// 输出文件路径
        #[arg(short, long)]
        output: Option<String>,
    },
}

#[tokio::main]
async fn main() -> Result<()> {
    let cli = Cli::parse();

    match cli.command {
        Commands::Add {
            config,
            env,
            server,
            ports,
        } => {
            add_proxy_environment(&config, &env, &server, &ports).await?;
        }
        Commands::Remove { config, env } => {
            remove_proxy_environment(&config, &env).await?;
        }
        Commands::List { config, format } => {
            list_proxy_environments(&config, &format).await?;
        }
        Commands::Validate { config } => {
            validate_proxy_config(&config).await?;
        }
        Commands::Example { output } => {
            generate_example_config(output.as_deref()).await?;
        }
    }

    Ok(())
}

async fn add_proxy_environment(
    config_path: &str,
    env_name: &str,
    server: &str,
    ports: &[String],
) -> Result<()> {
    println!("添加代理转发环境: {}", env_name);

    // 验证端口映射格式
    for port in ports {
        if !is_valid_port_mapping(port) {
            return Err(anyhow::anyhow!("无效的端口映射格式: {}", port));
        }
    }

    // 加载现有配置
    let mut config = match TomlConfigLoader::new(config_path) {
        Ok(config) => config,
        Err(_) => {
            println!("配置文件不存在，创建新配置");
            TomlConfigLoader::default()
        }
    };

    // 获取或创建代理转发配置
    let mut proxy_config = config.get_proxy_forwards().unwrap_or_else(|| ProxyForwardConfig {
        environments: HashMap::new(),
    });

    // 添加新环境
    proxy_config.environments.insert(
        env_name.to_string(),
        ProxyForwardEnvironment {
            server: server.to_string(),
            port: ports.to_vec(),
        },
    );

    // 保存配置
    config.set_proxy_forwards(proxy_config);
    
    // 这里需要实现配置保存功能
    println!("代理转发环境 '{}' 已添加", env_name);
    println!("服务器: {}", server);
    println!("端口映射: {:?}", ports);

    Ok(())
}

async fn remove_proxy_environment(config_path: &str, env_name: &str) -> Result<()> {
    println!("删除代理转发环境: {}", env_name);

    let mut config = TomlConfigLoader::new(config_path)?;
    
    if let Some(mut proxy_config) = config.get_proxy_forwards() {
        if proxy_config.environments.remove(env_name).is_some() {
            config.set_proxy_forwards(proxy_config);
            println!("代理转发环境 '{}' 已删除", env_name);
        } else {
            println!("代理转发环境 '{}' 不存在", env_name);
        }
    } else {
        println!("未找到代理转发配置");
    }

    Ok(())
}

async fn list_proxy_environments(config_path: &str, format: &str) -> Result<()> {
    let config = TomlConfigLoader::new(config_path)?;
    
    if let Some(proxy_config) = config.get_proxy_forwards() {
        match format {
            "json" => {
                let json = serde_json::to_string_pretty(&proxy_config)?;
                println!("{}", json);
            }
            "table" | _ => {
                println!("代理转发配置:");
                println!("{:<15} {:<20} {:<30}", "环境", "服务器", "端口映射");
                println!("{}", "-".repeat(65));
                
                for (env_name, env_config) in &proxy_config.environments {
                    for (i, port) in env_config.port.iter().enumerate() {
                        if i == 0 {
                            println!("{:<15} {:<20} {:<30}", env_name, env_config.server, port);
                        } else {
                            println!("{:<15} {:<20} {:<30}", "", "", port);
                        }
                    }
                }
            }
        }
    } else {
        println!("未找到代理转发配置");
    }

    Ok(())
}

async fn validate_proxy_config(config_path: &str) -> Result<()> {
    println!("验证代理转发配置: {}", config_path);

    let config = TomlConfigLoader::new(config_path)?;
    
    if let Some(proxy_config) = config.get_proxy_forwards() {
        let mut errors = Vec::new();
        let mut warnings = Vec::new();

        for (env_name, env_config) in &proxy_config.environments {
            // 验证服务器地址
            if env_config.server.is_empty() {
                errors.push(format!("环境 '{}': 服务器地址不能为空", env_name));
            }

            // 验证端口映射
            let mut used_ports = std::collections::HashSet::new();
            for port_mapping in &env_config.port {
                if !is_valid_port_mapping(port_mapping) {
                    errors.push(format!("环境 '{}': 无效的端口映射格式 '{}'", env_name, port_mapping));
                } else if let Some((local_port, _)) = parse_port_mapping(port_mapping) {
                    if !used_ports.insert(local_port) {
                        errors.push(format!("环境 '{}': 重复的本地端口 {}", env_name, local_port));
                    }
                    if local_port < 1024 {
                        warnings.push(format!("环境 '{}': 使用特权端口 {} 可能需要管理员权限", env_name, local_port));
                    }
                }
            }
        }

        if errors.is_empty() {
            println!("✅ 配置验证通过");
        } else {
            println!("❌ 发现 {} 个错误:", errors.len());
            for error in &errors {
                println!("  - {}", error);
            }
        }

        if !warnings.is_empty() {
            println!("⚠️  发现 {} 个警告:", warnings.len());
            for warning in &warnings {
                println!("  - {}", warning);
            }
        }

        if !errors.is_empty() {
            return Err(anyhow::anyhow!("配置验证失败"));
        }
    } else {
        println!("未找到代理转发配置");
    }

    Ok(())
}

async fn generate_example_config(output_path: Option<&str>) -> Result<()> {
    let example_config = r#"# EasyTier 代理转发配置示例
instance_name = "proxy_client"
ipv4 = "*************"
network_name = "default"
network_secret = "your_secret_key"

# 代理转发配置
[proxy_forward.environments.server]
server = "*************"
port = [
    "400-22",      # SSH
    "401-3306",    # MySQL
    "402-80",      # HTTP
    "403-443"      # HTTPS
]

[proxy_forward.environments.jumpserver]
server = "*************"
port = ["500-22"]

# EasyTier 网络配置
listeners = ["tcp://0.0.0.0:11010", "udp://0.0.0.0:11010"]
peers = ["tcp://peer_ip:11010"]
"#;

    match output_path {
        Some(path) => {
            std::fs::write(path, example_config)?;
            println!("示例配置已保存到: {}", path);
        }
        None => {
            println!("{}", example_config);
        }
    }

    Ok(())
}

fn is_valid_port_mapping(mapping: &str) -> bool {
    parse_port_mapping(mapping).is_some()
}

fn parse_port_mapping(mapping: &str) -> Option<(u16, u16)> {
    let parts: Vec<&str> = mapping.split('-').collect();
    if parts.len() != 2 {
        return None;
    }

    let local_port = parts[0].parse::<u16>().ok()?;
    let remote_port = parts[1].parse::<u16>().ok()?;

    Some((local_port, remote_port))
}
